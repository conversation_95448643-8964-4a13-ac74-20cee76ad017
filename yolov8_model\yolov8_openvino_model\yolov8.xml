<?xml version="1.0"?>
<net name="torch_jit" version="11">
	<layers>
		<layer id="0" name="images" type="Parameter" version="opset1">
			<data shape="1,3,384,640" element_type="f32" />
			<rt_info>
				<attribute name="old_api_map_element_type" version="0" value="f16" />
			</rt_info>
			<output>
				<port id="0" precision="FP32" names="images">
					<dim>1</dim>
					<dim>3</dim>
					<dim>384</dim>
					<dim>640</dim>
				</port>
			</output>
		</layer>
		<layer id="1" name="/model.22/Constant_9_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 2, 5040" offset="0" size="20160" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>2</dim>
					<dim>5040</dim>
				</port>
			</output>
		</layer>
		<layer id="2" name="/model.22/Constant_9" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>2</dim>
					<dim>5040</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.22/Constant_9_output_0">
					<dim>1</dim>
					<dim>2</dim>
					<dim>5040</dim>
				</port>
			</output>
		</layer>
		<layer id="3" name="model.0.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="16, 3, 3, 3" offset="20160" size="864" />
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="4" name="model.0.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.0.conv.weight">
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="5" name="/model.0/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="2, 2" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>3</dim>
					<dim>384</dim>
					<dim>640</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>192</dim>
					<dim>320</dim>
				</port>
			</output>
		</layer>
		<layer id="6" name="Reshape_140_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 16, 1, 1" offset="21024" size="32" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="7" name="Reshape_140" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="8" name="/model.0/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>192</dim>
					<dim>320</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.0/conv/Conv_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>192</dim>
					<dim>320</dim>
				</port>
			</output>
		</layer>
		<layer id="9" name="/model.0/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>192</dim>
					<dim>320</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.0/act/Mul_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>192</dim>
					<dim>320</dim>
				</port>
			</output>
		</layer>
		<layer id="10" name="model.1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 16, 3, 3" offset="21056" size="9216" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="11" name="model.1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.1.conv.weight">
					<dim>32</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="12" name="/model.1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="2, 2" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>192</dim>
					<dim>320</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="13" name="Reshape_157_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="30272" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="14" name="Reshape_157" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="15" name="/model.1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="16" name="/model.1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.1/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="17" name="model.2.cv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 32, 1, 1" offset="30336" size="2048" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="18" name="model.2.cv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.2.cv1.conv.weight">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="19" name="/model.2/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="20" name="Reshape_174_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="32384" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="21" name="Reshape_174" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="22" name="/model.2/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.2/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="23" name="/model.2/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.2/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="24" name="Constant_181" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="32448" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="25" name="Constant_9" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="32456" size="16" />
			<output>
				<port id="0" precision="I64" names="onnx::Split_137">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="26" name="/model.2/Split" type="VariadicSplit" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/model.2/Split_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
				<port id="4" precision="FP32" names="/model.2/Split_output_1">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="27" name="model.2.m.0.cv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="16, 16, 3, 3" offset="32472" size="4608" />
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="28" name="model.2.m.0.cv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.2.m.0.cv1.conv.weight">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="29" name="/model.2/m.0/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="30" name="Reshape_194_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 16, 1, 1" offset="37080" size="32" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="31" name="Reshape_194" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="32" name="/model.2/m.0/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.2/m.0/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="33" name="/model.2/m.0/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.2/m.0/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="34" name="model.2.m.0.cv2.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="16, 16, 3, 3" offset="37112" size="4608" />
			<output>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="35" name="model.2.m.0.cv2.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.2.m.0.cv2.conv.weight">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="36" name="/model.2/m.0/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>16</dim>
					<dim>16</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="37" name="Reshape_211_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 16, 1, 1" offset="41720" size="32" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="38" name="Reshape_211" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="39" name="/model.2/m.0/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.2/m.0/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="40" name="/model.2/m.0/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.2/m.0/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="41" name="/model.2/m.0/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.2/m.0/Add_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="42" name="/model.2/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/model.2/Concat_output_0">
					<dim>1</dim>
					<dim>48</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="43" name="model.2.cv2.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 48, 1, 1" offset="41752" size="3072" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>48</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="44" name="model.2.cv2.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>48</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.2.cv2.conv.weight">
					<dim>32</dim>
					<dim>48</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="45" name="/model.2/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>48</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>48</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="46" name="Reshape_230_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="44824" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="47" name="Reshape_230" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="48" name="/model.2/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.2/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="49" name="/model.2/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.2/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
			</output>
		</layer>
		<layer id="50" name="model.3.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 32, 3, 3" offset="44888" size="36864" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="51" name="model.3.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.3.conv.weight">
					<dim>64</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="52" name="/model.3/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="2, 2" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>96</dim>
					<dim>160</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="53" name="Reshape_247_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="81752" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="54" name="Reshape_247" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="55" name="/model.3/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.3/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="56" name="/model.3/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.3/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="57" name="model.4.cv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 1, 1" offset="81880" size="8192" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="58" name="model.4.cv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.4.cv1.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="59" name="/model.4/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="60" name="Reshape_264_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="90072" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="61" name="Reshape_264" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="62" name="/model.4/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="63" name="/model.4/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.4/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="64" name="Constant_271" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="32448" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="65" name="Constant_28" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="90200" size="16" />
			<output>
				<port id="0" precision="I64" names="onnx::Split_157">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="66" name="/model.4/Split" type="VariadicSplit" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/model.4/Split_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="4" precision="FP32" names="/model.4/Split_output_1">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="67" name="model.4.m.0.cv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 32, 3, 3" offset="90216" size="18432" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="68" name="model.4.m.0.cv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.4.m.0.cv1.conv.weight">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="69" name="/model.4/m.0/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="70" name="Reshape_284_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="108648" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="71" name="Reshape_284" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="72" name="/model.4/m.0/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/m.0/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="73" name="/model.4/m.0/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.4/m.0/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="74" name="model.4.m.0.cv2.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 32, 3, 3" offset="108712" size="18432" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="75" name="model.4.m.0.cv2.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.4.m.0.cv2.conv.weight">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="76" name="/model.4/m.0/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="77" name="Reshape_301_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="127144" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="78" name="Reshape_301" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="79" name="/model.4/m.0/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/m.0/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="80" name="/model.4/m.0/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.4/m.0/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="81" name="/model.4/m.0/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/m.0/Add_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="82" name="model.4.m.1.cv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 32, 3, 3" offset="127208" size="18432" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="83" name="model.4.m.1.cv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.4.m.1.cv1.conv.weight">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="84" name="/model.4/m.1/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="85" name="Reshape_319_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="145640" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="86" name="Reshape_319" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="87" name="/model.4/m.1/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/m.1/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="88" name="/model.4/m.1/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.4/m.1/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="89" name="model.4.m.1.cv2.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 32, 3, 3" offset="145704" size="18432" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="90" name="model.4.m.1.cv2.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.4.m.1.cv2.conv.weight">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="91" name="/model.4/m.1/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="92" name="Reshape_336_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="164136" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="93" name="Reshape_336" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="94" name="/model.4/m.1/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/m.1/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="95" name="/model.4/m.1/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.4/m.1/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="96" name="/model.4/m.1/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/m.1/Add_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="97" name="/model.4/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="3" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="FP32" names="/model.4/Concat_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="98" name="model.4.cv2.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 128, 1, 1" offset="164200" size="16384" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="99" name="model.4.cv2.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.4.cv2.conv.weight">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="100" name="/model.4/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="101" name="Reshape_355_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="180584" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="102" name="Reshape_355" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="103" name="/model.4/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.4/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="104" name="/model.4/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.4/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="105" name="model.5.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 64, 3, 3" offset="180712" size="147456" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="106" name="model.5.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.5.conv.weight">
					<dim>128</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="107" name="/model.5/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="2, 2" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="108" name="Reshape_372_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="328168" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="109" name="Reshape_372" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="110" name="/model.5/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.5/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="111" name="/model.5/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.5/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="112" name="model.6.cv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 128, 1, 1" offset="328424" size="32768" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="113" name="model.6.cv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.6.cv1.conv.weight">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="114" name="/model.6/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="115" name="Reshape_389_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="361192" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="116" name="Reshape_389" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="117" name="/model.6/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="118" name="/model.6/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.6/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="119" name="Constant_396" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="32448" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="120" name="Constant_54" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="361448" size="16" />
			<output>
				<port id="0" precision="I64" names="onnx::Split_184">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="121" name="/model.6/Split" type="VariadicSplit" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/model.6/Split_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="4" precision="FP32" names="/model.6/Split_output_1">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="122" name="model.6.m.0.cv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 3, 3" offset="361464" size="73728" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="123" name="model.6.m.0.cv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.6.m.0.cv1.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="124" name="/model.6/m.0/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="125" name="Reshape_409_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="435192" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="126" name="Reshape_409" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="127" name="/model.6/m.0/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/m.0/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="128" name="/model.6/m.0/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.6/m.0/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="129" name="model.6.m.0.cv2.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 3, 3" offset="435320" size="73728" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="130" name="model.6.m.0.cv2.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.6.m.0.cv2.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="131" name="/model.6/m.0/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="132" name="Reshape_426_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="509048" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="133" name="Reshape_426" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="134" name="/model.6/m.0/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/m.0/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="135" name="/model.6/m.0/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.6/m.0/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="136" name="/model.6/m.0/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/m.0/Add_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="137" name="model.6.m.1.cv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 3, 3" offset="509176" size="73728" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="138" name="model.6.m.1.cv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.6.m.1.cv1.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="139" name="/model.6/m.1/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="140" name="Reshape_444_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="582904" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="141" name="Reshape_444" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="142" name="/model.6/m.1/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/m.1/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="143" name="/model.6/m.1/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.6/m.1/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="144" name="model.6.m.1.cv2.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 3, 3" offset="583032" size="73728" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="145" name="model.6.m.1.cv2.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.6.m.1.cv2.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="146" name="/model.6/m.1/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="147" name="Reshape_461_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="656760" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="148" name="Reshape_461" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="149" name="/model.6/m.1/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/m.1/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="150" name="/model.6/m.1/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.6/m.1/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="151" name="/model.6/m.1/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/m.1/Add_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="152" name="/model.6/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="3" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="FP32" names="/model.6/Concat_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="153" name="model.6.cv2.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 256, 1, 1" offset="656888" size="65536" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="154" name="model.6.cv2.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.6.cv2.conv.weight">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="155" name="/model.6/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="156" name="Reshape_480_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="722424" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="157" name="Reshape_480" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="158" name="/model.6/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.6/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="159" name="/model.6/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.6/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="160" name="model.7.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="256, 128, 3, 3" offset="722680" size="589824" />
			<output>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="161" name="model.7.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.7.conv.weight">
					<dim>256</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="162" name="/model.7/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="2, 2" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>256</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="163" name="Reshape_497_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 256, 1, 1" offset="1312504" size="512" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="164" name="Reshape_497" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="165" name="/model.7/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.7/conv/Conv_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="166" name="/model.7/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.7/act/Mul_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="167" name="model.8.cv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="256, 256, 1, 1" offset="1313016" size="131072" />
			<output>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="168" name="model.8.cv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.8.cv1.conv.weight">
					<dim>256</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="169" name="/model.8/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>256</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="170" name="Reshape_514_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 256, 1, 1" offset="1444088" size="512" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="171" name="Reshape_514" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="172" name="/model.8/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.8/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="173" name="/model.8/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.8/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="174" name="Constant_521" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="32448" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="175" name="Constant_80" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="1444600" size="16" />
			<output>
				<port id="0" precision="I64" names="onnx::Split_211">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="176" name="/model.8/Split" type="VariadicSplit" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/model.8/Split_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="4" precision="FP32" names="/model.8/Split_output_1">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="177" name="model.8.m.0.cv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 128, 3, 3" offset="1444616" size="294912" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="178" name="model.8.m.0.cv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.8.m.0.cv1.conv.weight">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="179" name="/model.8/m.0/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="180" name="Reshape_534_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="1739528" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="181" name="Reshape_534" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="182" name="/model.8/m.0/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.8/m.0/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="183" name="/model.8/m.0/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.8/m.0/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="184" name="model.8.m.0.cv2.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 128, 3, 3" offset="1739784" size="294912" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="185" name="model.8.m.0.cv2.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.8.m.0.cv2.conv.weight">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="186" name="/model.8/m.0/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="187" name="Reshape_551_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="2034696" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="188" name="Reshape_551" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="189" name="/model.8/m.0/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.8/m.0/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="190" name="/model.8/m.0/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.8/m.0/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="191" name="/model.8/m.0/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.8/m.0/Add_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="192" name="/model.8/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/model.8/Concat_output_0">
					<dim>1</dim>
					<dim>384</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="193" name="model.8.cv2.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="256, 384, 1, 1" offset="2034952" size="196608" />
			<output>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>384</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="194" name="model.8.cv2.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>384</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.8.cv2.conv.weight">
					<dim>256</dim>
					<dim>384</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="195" name="/model.8/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>384</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>256</dim>
					<dim>384</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="196" name="Reshape_570_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 256, 1, 1" offset="2231560" size="512" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="197" name="Reshape_570" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="198" name="/model.8/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.8/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="199" name="/model.8/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.8/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="200" name="model.9.cv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 256, 1, 1" offset="2232072" size="65536" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="201" name="model.9.cv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.9.cv1.conv.weight">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="202" name="/model.9/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="203" name="Reshape_587_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="2297608" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="204" name="Reshape_587" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="205" name="/model.9/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.9/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="206" name="/model.9/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.9/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="207" name="/model.9/m/MaxPool" type="MaxPool" version="opset8">
			<data strides="1, 1" dilations="1, 1" pads_begin="2, 2" pads_end="2, 2" kernel="5, 5" rounding_type="floor" auto_pad="explicit" index_element_type="i64" axis="0" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.9/m/MaxPool_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="208" name="/model.9/m_1/MaxPool" type="MaxPool" version="opset8">
			<data strides="1, 1" dilations="1, 1" pads_begin="2, 2" pads_end="2, 2" kernel="5, 5" rounding_type="floor" auto_pad="explicit" index_element_type="i64" axis="0" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.9/m_1/MaxPool_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="209" name="/model.9/m_2/MaxPool" type="MaxPool" version="opset8">
			<data strides="1, 1" dilations="1, 1" pads_begin="2, 2" pads_end="2, 2" kernel="5, 5" rounding_type="floor" auto_pad="explicit" index_element_type="i64" axis="0" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.9/m_2/MaxPool_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="210" name="/model.9/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="3" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="FP32" names="/model.9/Concat_output_0">
					<dim>1</dim>
					<dim>512</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="211" name="model.9.cv2.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="256, 512, 1, 1" offset="2297864" size="262144" />
			<output>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="212" name="model.9.cv2.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.9.cv2.conv.weight">
					<dim>256</dim>
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="213" name="/model.9/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>512</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>256</dim>
					<dim>512</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="214" name="Reshape_608_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 256, 1, 1" offset="2560008" size="512" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="215" name="Reshape_608" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="216" name="/model.9/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.9/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="217" name="/model.9/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.9/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="218" name="/model.10/Constant" type="Const" version="opset1">
			<data element_type="f32" shape="4" offset="2560520" size="16" />
			<output>
				<port id="0" precision="FP32" names="/model.10/Constant_output_0">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="219" name="/model.10/Resize" type="Interpolate" version="opset11">
			<data mode="nearest" shape_calculation_mode="scales" coordinate_transformation_mode="asymmetric" nearest_mode="floor" antialias="false" pads_begin="0, 0, 0, 0" pads_end="0, 0, 0, 0" cube_coeff="-0.75" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.10/Resize_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="220" name="/model.11/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.11/Concat_output_0">
					<dim>1</dim>
					<dim>384</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="221" name="model.12.cv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 384, 1, 1" offset="2560536" size="98304" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>384</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="222" name="model.12.cv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>384</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.12.cv1.conv.weight">
					<dim>128</dim>
					<dim>384</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="223" name="/model.12/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>384</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>384</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="224" name="Reshape_629_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="2658840" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="225" name="Reshape_629" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="226" name="/model.12/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.12/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="227" name="/model.12/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.12/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="228" name="Constant_635" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="32448" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="229" name="/model.12/Split" type="VariadicSplit" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/model.12/Split_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="4" precision="FP32" names="/model.12/Split_output_1">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="230" name="model.12.m.0.cv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 3, 3" offset="2659096" size="73728" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="231" name="model.12.m.0.cv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.12.m.0.cv1.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="232" name="/model.12/m.0/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="233" name="Reshape_648_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="2732824" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="234" name="Reshape_648" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="235" name="/model.12/m.0/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.12/m.0/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="236" name="/model.12/m.0/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.12/m.0/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="237" name="model.12.m.0.cv2.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 3, 3" offset="2732952" size="73728" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="238" name="model.12.m.0.cv2.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.12.m.0.cv2.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="239" name="/model.12/m.0/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="240" name="Reshape_665_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="2806680" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="241" name="Reshape_665" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="242" name="/model.12/m.0/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.12/m.0/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="243" name="/model.12/m.0/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.12/m.0/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="244" name="/model.12/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/model.12/Concat_output_0">
					<dim>1</dim>
					<dim>192</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="245" name="model.12.cv2.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 192, 1, 1" offset="2806808" size="49152" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>192</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="246" name="model.12.cv2.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>192</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.12.cv2.conv.weight">
					<dim>128</dim>
					<dim>192</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="247" name="/model.12/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>192</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>192</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="248" name="Reshape_683_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="2855960" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="249" name="Reshape_683" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="250" name="/model.12/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.12/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="251" name="/model.12/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.12/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="252" name="/model.13/Constant" type="Const" version="opset1">
			<data element_type="f32" shape="4" offset="2560520" size="16" />
			<output>
				<port id="0" precision="FP32" names="/model.13/Constant_output_0">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="253" name="/model.13/Resize" type="Interpolate" version="opset11">
			<data mode="nearest" shape_calculation_mode="scales" coordinate_transformation_mode="asymmetric" nearest_mode="floor" antialias="false" pads_begin="0, 0, 0, 0" pads_end="0, 0, 0, 0" cube_coeff="-0.75" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.13/Resize_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="254" name="/model.14/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.14/Concat_output_0">
					<dim>1</dim>
					<dim>192</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="255" name="model.15.cv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 192, 1, 1" offset="2856216" size="24576" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>192</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="256" name="model.15.cv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>192</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.15.cv1.conv.weight">
					<dim>64</dim>
					<dim>192</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="257" name="/model.15/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>192</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>192</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="258" name="Reshape_704_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="2880792" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="259" name="Reshape_704" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="260" name="/model.15/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.15/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="261" name="/model.15/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.15/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="262" name="Constant_710" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="32448" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="263" name="/model.15/Split" type="VariadicSplit" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/model.15/Split_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="4" precision="FP32" names="/model.15/Split_output_1">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="264" name="model.15.m.0.cv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 32, 3, 3" offset="2880920" size="18432" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="265" name="model.15.m.0.cv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.15.m.0.cv1.conv.weight">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="266" name="/model.15/m.0/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="267" name="Reshape_723_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="2899352" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="268" name="Reshape_723" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="269" name="/model.15/m.0/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.15/m.0/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="270" name="/model.15/m.0/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.15/m.0/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="271" name="model.15.m.0.cv2.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="32, 32, 3, 3" offset="2899416" size="18432" />
			<output>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="272" name="model.15.m.0.cv2.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.15.m.0.cv2.conv.weight">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="273" name="/model.15/m.0/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>32</dim>
					<dim>32</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="274" name="Reshape_740_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 32, 1, 1" offset="2917848" size="64" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="275" name="Reshape_740" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="276" name="/model.15/m.0/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.15/m.0/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="277" name="/model.15/m.0/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.15/m.0/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="278" name="/model.15/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>32</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/model.15/Concat_output_0">
					<dim>1</dim>
					<dim>96</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="279" name="model.15.cv2.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 96, 1, 1" offset="2917912" size="12288" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>96</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="280" name="model.15.cv2.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>96</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.15.cv2.conv.weight">
					<dim>64</dim>
					<dim>96</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="281" name="/model.15/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>96</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>96</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="282" name="Reshape_758_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="2930200" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="283" name="Reshape_758" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="284" name="/model.15/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.15/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="285" name="/model.15/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.15/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="286" name="model.22.cv2.0.0.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 3, 3" offset="2930328" size="73728" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="287" name="model.22.cv2.0.0.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.22.cv2.0.0.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="288" name="/model.22/cv2.0/cv2.0.0/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="289" name="Reshape_953_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="3004056" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="290" name="Reshape_953" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="291" name="/model.22/cv2.0/cv2.0.0/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/cv2.0/cv2.0.0/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="292" name="/model.22/cv2.0/cv2.0.0/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.22/cv2.0/cv2.0.0/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="293" name="model.22.cv2.0.1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 3, 3" offset="3004184" size="73728" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="294" name="model.22.cv2.0.1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.22.cv2.0.1.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="295" name="/model.22/cv2.0/cv2.0.1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="296" name="Reshape_970_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="3077912" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="297" name="Reshape_970" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="298" name="/model.22/cv2.0/cv2.0.1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/cv2.0/cv2.0.1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="299" name="/model.22/cv2.0/cv2.0.1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.22/cv2.0/cv2.0.1/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="300" name="model.22.cv2.0.2.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 1, 1" offset="3078040" size="8192" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="301" name="model.22.cv2.0.2.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.22.cv2.0.2.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="302" name="/model.22/cv2.0/cv2.0.2/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="303" name="Reshape_987_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="3086232" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="304" name="Reshape_987" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="305" name="/model.22/cv2.0/cv2.0.2/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/cv2.0/cv2.0.2/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="306" name="model.22.cv3.0.0.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 3, 3" offset="3086360" size="73728" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="307" name="model.22.cv3.0.0.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.22.cv3.0.0.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="308" name="/model.22/cv3.0/cv3.0.0/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="309" name="Reshape_1002_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="3160088" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="310" name="Reshape_1002" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="311" name="/model.22/cv3.0/cv3.0.0/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/cv3.0/cv3.0.0/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="312" name="/model.22/cv3.0/cv3.0.0/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.22/cv3.0/cv3.0.0/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="313" name="model.22.cv3.0.1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 3, 3" offset="3160216" size="73728" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="314" name="model.22.cv3.0.1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.22.cv3.0.1.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="315" name="/model.22/cv3.0/cv3.0.1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="316" name="Reshape_1019_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="3233944" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="317" name="Reshape_1019" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="318" name="/model.22/cv3.0/cv3.0.1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/cv3.0/cv3.0.1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="319" name="/model.22/cv3.0/cv3.0.1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.22/cv3.0/cv3.0.1/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="320" name="model.22.cv3.0.2.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="4, 64, 1, 1" offset="3234072" size="512" />
			<output>
				<port id="0" precision="FP16">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="321" name="model.22.cv3.0.2.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.22.cv3.0.2.weight">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="322" name="/model.22/cv3.0/cv3.0.2/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="323" name="Reshape_1036_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 4, 1, 1" offset="3234584" size="8" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="324" name="Reshape_1036" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="325" name="/model.22/cv3.0/cv3.0.2/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/cv3.0/cv3.0.2/Conv_output_0">
					<dim>1</dim>
					<dim>4</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="326" name="/model.22/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/Concat_output_0">
					<dim>1</dim>
					<dim>68</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
			</output>
		</layer>
		<layer id="327" name="/model.22/Constant" type="Const" version="opset1">
			<data element_type="i64" shape="3" offset="3234592" size="24" />
			<output>
				<port id="0" precision="I64" names="/model.22/Constant_output_0">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="328" name="/model.22/Reshape" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>68</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/Reshape_output_0">
					<dim>1</dim>
					<dim>68</dim>
					<dim>3840</dim>
				</port>
			</output>
		</layer>
		<layer id="329" name="model.16.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 3, 3" offset="3234616" size="73728" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="330" name="model.16.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.16.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="331" name="/model.16/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="2, 2" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>48</dim>
					<dim>80</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="332" name="Reshape_775_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="3308344" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="333" name="Reshape_775" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="334" name="/model.16/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.16/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="335" name="/model.16/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.16/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="336" name="/model.17/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.17/Concat_output_0">
					<dim>1</dim>
					<dim>192</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="337" name="model.18.cv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 192, 1, 1" offset="3308472" size="49152" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>192</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="338" name="model.18.cv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>192</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.18.cv1.conv.weight">
					<dim>128</dim>
					<dim>192</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="339" name="/model.18/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>192</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>192</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="340" name="Reshape_793_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="3357624" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="341" name="Reshape_793" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="342" name="/model.18/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.18/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="343" name="/model.18/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.18/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="344" name="Constant_799" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="32448" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="345" name="/model.18/Split" type="VariadicSplit" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/model.18/Split_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="4" precision="FP32" names="/model.18/Split_output_1">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="346" name="model.18.m.0.cv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 3, 3" offset="3357880" size="73728" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="347" name="model.18.m.0.cv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.18.m.0.cv1.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="348" name="/model.18/m.0/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="349" name="Reshape_812_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="3431608" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="350" name="Reshape_812" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="351" name="/model.18/m.0/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.18/m.0/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="352" name="/model.18/m.0/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.18/m.0/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="353" name="model.18.m.0.cv2.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 3, 3" offset="3431736" size="73728" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="354" name="model.18.m.0.cv2.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.18.m.0.cv2.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="355" name="/model.18/m.0/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="356" name="Reshape_829_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="3505464" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="357" name="Reshape_829" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="358" name="/model.18/m.0/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.18/m.0/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="359" name="/model.18/m.0/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.18/m.0/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="360" name="/model.18/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/model.18/Concat_output_0">
					<dim>1</dim>
					<dim>192</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="361" name="model.18.cv2.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 192, 1, 1" offset="3505592" size="49152" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>192</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="362" name="model.18.cv2.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>192</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.18.cv2.conv.weight">
					<dim>128</dim>
					<dim>192</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="363" name="/model.18/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>192</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>192</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="364" name="Reshape_847_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="3554744" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="365" name="Reshape_847" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="366" name="/model.18/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.18/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="367" name="/model.18/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.18/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="368" name="model.22.cv2.1.0.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 128, 3, 3" offset="3555000" size="147456" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="369" name="model.22.cv2.1.0.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.22.cv2.1.0.conv.weight">
					<dim>64</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="370" name="/model.22/cv2.1/cv2.1.0/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="371" name="Reshape_1052_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="3702456" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="372" name="Reshape_1052" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="373" name="/model.22/cv2.1/cv2.1.0/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/cv2.1/cv2.1.0/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="374" name="/model.22/cv2.1/cv2.1.0/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.22/cv2.1/cv2.1.0/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="375" name="model.22.cv2.1.1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 3, 3" offset="3702584" size="73728" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="376" name="model.22.cv2.1.1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.22.cv2.1.1.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="377" name="/model.22/cv2.1/cv2.1.1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="378" name="Reshape_1069_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="3776312" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="379" name="Reshape_1069" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="380" name="/model.22/cv2.1/cv2.1.1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/cv2.1/cv2.1.1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="381" name="/model.22/cv2.1/cv2.1.1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.22/cv2.1/cv2.1.1/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="382" name="model.22.cv2.1.2.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 1, 1" offset="3776440" size="8192" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="383" name="model.22.cv2.1.2.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.22.cv2.1.2.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="384" name="/model.22/cv2.1/cv2.1.2/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="385" name="Reshape_1086_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="3784632" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="386" name="Reshape_1086" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="387" name="/model.22/cv2.1/cv2.1.2/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/cv2.1/cv2.1.2/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="388" name="model.22.cv3.1.0.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 128, 3, 3" offset="3784760" size="147456" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="389" name="model.22.cv3.1.0.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.22.cv3.1.0.conv.weight">
					<dim>64</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="390" name="/model.22/cv3.1/cv3.1.0/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="391" name="Reshape_1101_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="3932216" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="392" name="Reshape_1101" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="393" name="/model.22/cv3.1/cv3.1.0/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/cv3.1/cv3.1.0/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="394" name="/model.22/cv3.1/cv3.1.0/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.22/cv3.1/cv3.1.0/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="395" name="model.22.cv3.1.1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 3, 3" offset="3932344" size="73728" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="396" name="model.22.cv3.1.1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.22.cv3.1.1.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="397" name="/model.22/cv3.1/cv3.1.1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="398" name="Reshape_1118_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="4006072" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="399" name="Reshape_1118" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="400" name="/model.22/cv3.1/cv3.1.1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/cv3.1/cv3.1.1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="401" name="/model.22/cv3.1/cv3.1.1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.22/cv3.1/cv3.1.1/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="402" name="model.22.cv3.1.2.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="4, 64, 1, 1" offset="4006200" size="512" />
			<output>
				<port id="0" precision="FP16">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="403" name="model.22.cv3.1.2.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.22.cv3.1.2.weight">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="404" name="/model.22/cv3.1/cv3.1.2/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="405" name="Reshape_1135_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 4, 1, 1" offset="4006712" size="8" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="406" name="Reshape_1135" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="407" name="/model.22/cv3.1/cv3.1.2/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/cv3.1/cv3.1.2/Conv_output_0">
					<dim>1</dim>
					<dim>4</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="408" name="/model.22/Concat_1" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/Concat_1_output_0">
					<dim>1</dim>
					<dim>68</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
			</output>
		</layer>
		<layer id="409" name="/model.22/Constant_1" type="Const" version="opset1">
			<data element_type="i64" shape="3" offset="3234592" size="24" />
			<output>
				<port id="0" precision="I64" names="/model.22/Constant_1_output_0">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="410" name="/model.22/Reshape_1" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>68</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/Reshape_1_output_0">
					<dim>1</dim>
					<dim>68</dim>
					<dim>960</dim>
				</port>
			</output>
		</layer>
		<layer id="411" name="model.19.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 128, 3, 3" offset="4006720" size="294912" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="412" name="model.19.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.19.conv.weight">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="413" name="/model.19/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="2, 2" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>24</dim>
					<dim>40</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="414" name="Reshape_864_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="4301632" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="415" name="Reshape_864" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="416" name="/model.19/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.19/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="417" name="/model.19/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.19/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="418" name="/model.20/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.20/Concat_output_0">
					<dim>1</dim>
					<dim>384</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="419" name="model.21.cv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="256, 384, 1, 1" offset="4301888" size="196608" />
			<output>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>384</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="420" name="model.21.cv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>384</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.21.cv1.conv.weight">
					<dim>256</dim>
					<dim>384</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="421" name="/model.21/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>384</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>256</dim>
					<dim>384</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="422" name="Reshape_882_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 256, 1, 1" offset="4498496" size="512" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="423" name="Reshape_882" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="424" name="/model.21/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.21/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="425" name="/model.21/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.21/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="426" name="Constant_888" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="32448" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="427" name="/model.21/Split" type="VariadicSplit" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/model.21/Split_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="4" precision="FP32" names="/model.21/Split_output_1">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="428" name="model.21.m.0.cv1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 128, 3, 3" offset="4499008" size="294912" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="429" name="model.21.m.0.cv1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.21.m.0.cv1.conv.weight">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="430" name="/model.21/m.0/cv1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="431" name="Reshape_901_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="4793920" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="432" name="Reshape_901" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="433" name="/model.21/m.0/cv1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.21/m.0/cv1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="434" name="/model.21/m.0/cv1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.21/m.0/cv1/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="435" name="model.21.m.0.cv2.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="128, 128, 3, 3" offset="4794176" size="294912" />
			<output>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="436" name="model.21.m.0.cv2.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.21.m.0.cv2.conv.weight">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="437" name="/model.21/m.0/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>128</dim>
					<dim>128</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="438" name="Reshape_918_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 128, 1, 1" offset="5089088" size="256" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="439" name="Reshape_918" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="440" name="/model.21/m.0/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.21/m.0/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="441" name="/model.21/m.0/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.21/m.0/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="442" name="/model.21/Concat" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>128</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/model.21/Concat_output_0">
					<dim>1</dim>
					<dim>384</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="443" name="model.21.cv2.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="256, 384, 1, 1" offset="5089344" size="196608" />
			<output>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>384</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="444" name="model.21.cv2.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>256</dim>
					<dim>384</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.21.cv2.conv.weight">
					<dim>256</dim>
					<dim>384</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="445" name="/model.21/cv2/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>384</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>256</dim>
					<dim>384</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="446" name="Reshape_936_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 256, 1, 1" offset="5285952" size="512" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="447" name="Reshape_936" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="448" name="/model.21/cv2/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.21/cv2/conv/Conv_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="449" name="/model.21/cv2/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.21/cv2/act/Mul_output_0">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="450" name="model.22.cv2.2.0.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 256, 3, 3" offset="5286464" size="294912" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>256</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="451" name="model.22.cv2.2.0.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>256</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.22.cv2.2.0.conv.weight">
					<dim>64</dim>
					<dim>256</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="452" name="/model.22/cv2.2/cv2.2.0/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>256</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="453" name="Reshape_1151_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="5581376" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="454" name="Reshape_1151" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="455" name="/model.22/cv2.2/cv2.2.0/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/cv2.2/cv2.2.0/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="456" name="/model.22/cv2.2/cv2.2.0/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.22/cv2.2/cv2.2.0/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="457" name="model.22.cv2.2.1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 3, 3" offset="5581504" size="73728" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="458" name="model.22.cv2.2.1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.22.cv2.2.1.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="459" name="/model.22/cv2.2/cv2.2.1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="460" name="Reshape_1168_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="5655232" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="461" name="Reshape_1168" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="462" name="/model.22/cv2.2/cv2.2.1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/cv2.2/cv2.2.1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="463" name="/model.22/cv2.2/cv2.2.1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.22/cv2.2/cv2.2.1/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="464" name="model.22.cv2.2.2.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 1, 1" offset="5655360" size="8192" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="465" name="model.22.cv2.2.2.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.22.cv2.2.2.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="466" name="/model.22/cv2.2/cv2.2.2/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="467" name="Reshape_1185_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="5663552" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="468" name="Reshape_1185" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="469" name="/model.22/cv2.2/cv2.2.2/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/cv2.2/cv2.2.2/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="470" name="model.22.cv3.2.0.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 256, 3, 3" offset="5663680" size="294912" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>256</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="471" name="model.22.cv3.2.0.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>256</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.22.cv3.2.0.conv.weight">
					<dim>64</dim>
					<dim>256</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="472" name="/model.22/cv3.2/cv3.2.0/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>256</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>256</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="473" name="Reshape_1200_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="5958592" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="474" name="Reshape_1200" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="475" name="/model.22/cv3.2/cv3.2.0/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/cv3.2/cv3.2.0/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="476" name="/model.22/cv3.2/cv3.2.0/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.22/cv3.2/cv3.2.0/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="477" name="model.22.cv3.2.1.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="64, 64, 3, 3" offset="5958720" size="73728" />
			<output>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="478" name="model.22.cv3.2.1.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.22.cv3.2.1.conv.weight">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="479" name="/model.22/cv3.2/cv3.2.1/conv/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="1, 1" pads_end="1, 1" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>64</dim>
					<dim>64</dim>
					<dim>3</dim>
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="480" name="Reshape_1217_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 64, 1, 1" offset="6032448" size="128" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="481" name="Reshape_1217" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="482" name="/model.22/cv3.2/cv3.2.1/conv/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/cv3.2/cv3.2.1/conv/Conv_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="483" name="/model.22/cv3.2/cv3.2.1/act/Mul" type="Swish" version="opset4">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.22/cv3.2/cv3.2.1/act/Mul_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="484" name="model.22.cv3.2.2.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="4, 64, 1, 1" offset="6032576" size="512" />
			<output>
				<port id="0" precision="FP16">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="485" name="model.22.cv3.2.2.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.22.cv3.2.2.weight">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="486" name="/model.22/cv3.2/cv3.2.2/Conv/WithoutBiases" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>4</dim>
					<dim>64</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="487" name="Reshape_1234_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 4, 1, 1" offset="6033088" size="8" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="488" name="Reshape_1234" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="489" name="/model.22/cv3.2/cv3.2.2/Conv" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/cv3.2/cv3.2.2/Conv_output_0">
					<dim>1</dim>
					<dim>4</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="490" name="/model.22/Concat_2" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/Concat_2_output_0">
					<dim>1</dim>
					<dim>68</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
			</output>
		</layer>
		<layer id="491" name="/model.22/Constant_2" type="Const" version="opset1">
			<data element_type="i64" shape="3" offset="3234592" size="24" />
			<output>
				<port id="0" precision="I64" names="/model.22/Constant_2_output_0">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="492" name="/model.22/Reshape_2" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>68</dim>
					<dim>12</dim>
					<dim>20</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/Reshape_2_output_0">
					<dim>1</dim>
					<dim>68</dim>
					<dim>240</dim>
				</port>
			</output>
		</layer>
		<layer id="493" name="/model.22/Concat_3" type="Concat" version="opset1">
			<data axis="2" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>68</dim>
					<dim>3840</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>68</dim>
					<dim>960</dim>
				</port>
				<port id="2" precision="FP32">
					<dim>1</dim>
					<dim>68</dim>
					<dim>240</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/model.22/Concat_3_output_0">
					<dim>1</dim>
					<dim>68</dim>
					<dim>5040</dim>
				</port>
			</output>
		</layer>
		<layer id="494" name="Constant_1253" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="32448" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="495" name="Constant_225" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="6033096" size="16" />
			<output>
				<port id="0" precision="I64" names="onnx::Split_388">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="496" name="/model.22/Split" type="VariadicSplit" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>68</dim>
					<dim>5040</dim>
				</port>
				<port id="1" precision="I64" />
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="3" precision="FP32" names="/model.22/Split_output_0">
					<dim>1</dim>
					<dim>64</dim>
					<dim>5040</dim>
				</port>
				<port id="4" precision="FP32" names="/model.22/Split_output_1">
					<dim>1</dim>
					<dim>4</dim>
					<dim>5040</dim>
				</port>
			</output>
		</layer>
		<layer id="497" name="/model.22/dfl/Constant" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="6033112" size="32" />
			<output>
				<port id="0" precision="I64" names="/model.22/dfl/Constant_output_0">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="498" name="/model.22/dfl/Reshape" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>64</dim>
					<dim>5040</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/dfl/Reshape_output_0">
					<dim>1</dim>
					<dim>4</dim>
					<dim>16</dim>
					<dim>5040</dim>
				</port>
			</output>
		</layer>
		<layer id="499" name="Constant_1259" type="Const" version="opset1">
			<data element_type="i64" shape="4" offset="6033144" size="32" />
			<output>
				<port id="0" precision="I64">
					<dim>4</dim>
				</port>
			</output>
		</layer>
		<layer id="500" name="/model.22/dfl/Transpose" type="Transpose" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>16</dim>
					<dim>5040</dim>
				</port>
				<port id="1" precision="I64">
					<dim>4</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/dfl/Transpose_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>4</dim>
					<dim>5040</dim>
				</port>
			</output>
		</layer>
		<layer id="501" name="/model.22/dfl/Softmax" type="SoftMax" version="opset8">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>4</dim>
					<dim>5040</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.22/dfl/Softmax_output_0">
					<dim>1</dim>
					<dim>16</dim>
					<dim>4</dim>
					<dim>5040</dim>
				</port>
			</output>
		</layer>
		<layer id="502" name="model.22.dfl.conv.weight_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 16, 1, 1" offset="6033176" size="32" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="503" name="model.22.dfl.conv.weight" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="model.22.dfl.conv.weight">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="504" name="/model.22/dfl/conv/Conv" type="Convolution" version="opset1">
			<data strides="1, 1" dilations="1, 1" pads_begin="0, 0" pads_end="0, 0" auto_pad="explicit" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>4</dim>
					<dim>5040</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>16</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/dfl/conv/Conv_output_0">
					<dim>1</dim>
					<dim>1</dim>
					<dim>4</dim>
					<dim>5040</dim>
				</port>
			</output>
		</layer>
		<layer id="505" name="/model.22/dfl/Constant_1" type="Const" version="opset1">
			<data element_type="i64" shape="3" offset="6033208" size="24" />
			<output>
				<port id="0" precision="I64" names="/model.22/dfl/Constant_1_output_0">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="506" name="/model.22/dfl/Reshape_1" type="Reshape" version="opset1">
			<data special_zero="true" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>4</dim>
					<dim>5040</dim>
				</port>
				<port id="1" precision="I64">
					<dim>3</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/dfl/Reshape_1_output_0">
					<dim>1</dim>
					<dim>4</dim>
					<dim>5040</dim>
				</port>
			</output>
		</layer>
		<layer id="507" name="Constant_3556" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="6033232" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="508" name="Constant_3557" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="6033232" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="509" name="Constant_3553" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="32448" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="510" name="/model.22/Shape" type="ShapeOf" version="opset3">
			<data output_type="i64" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>5040</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="I64" names="/model.22/Shape_output_0">
					<dim>3</dim>
				</port>
			</output>
		</layer>
		<layer id="511" name="/model.22/Constant_3" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="32448" size="8" />
			<output>
				<port id="0" precision="I64" names="/model.22/Constant_3_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="512" name="Constant_1270" type="Const" version="opset1">
			<data element_type="i64" shape="" offset="6033248" size="8" />
			<output>
				<port id="0" precision="I64" />
			</output>
		</layer>
		<layer id="513" name="/model.22/Gather" type="Gather" version="opset8">
			<data batch_dims="0" />
			<input>
				<port id="0" precision="I64">
					<dim>3</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
				<port id="2" precision="I64" />
			</input>
			<output>
				<port id="3" precision="I64" names="/model.22/Gather_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="514" name="/model.22/Constant_5" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="32448" size="8" />
			<output>
				<port id="0" precision="I64" names="/model.22/Constant_5_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="515" name="/model.22/Add" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/model.22/Add_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="516" name="/model.22/Constant_6" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="6033256" size="8" />
			<output>
				<port id="0" precision="I64" names="/model.22/Constant_6_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="517" name="/model.22/Div" type="Divide" version="opset1">
			<data auto_broadcast="numpy" m_pythondiv="true" />
			<input>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/model.22/Div_output_0,/model.22/Mul_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="518" name="Constant_3552" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="6033264" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="519" name="ScatterUpdate_3558" type="ScatterUpdate" version="opset3">
			<input>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
				</port>
				<port id="3" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="520" name="Constant_3561" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="6033268" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="521" name="/model.22/Slice" type="StridedSlice" version="opset1">
			<data begin_mask="1, 0" end_mask="1, 0" new_axis_mask="" shrink_axis_mask="" ellipsis_mask="" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>5040</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
				<port id="3" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="FP32" names="/model.22/Slice_output_0">
					<dim>1</dim>
					<dim>2</dim>
					<dim>5040</dim>
				</port>
			</output>
		</layer>
		<layer id="522" name="/model.22/Sub" type="Subtract" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>2</dim>
					<dim>5040</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>2</dim>
					<dim>5040</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/Sub_output_0">
					<dim>1</dim>
					<dim>2</dim>
					<dim>5040</dim>
				</port>
			</output>
		</layer>
		<layer id="523" name="/model.22/Constant_10_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 2, 5040" offset="0" size="20160" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>2</dim>
					<dim>5040</dim>
				</port>
			</output>
		</layer>
		<layer id="524" name="/model.22/Constant_10" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>2</dim>
					<dim>5040</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.22/Constant_10_output_0">
					<dim>1</dim>
					<dim>2</dim>
					<dim>5040</dim>
				</port>
			</output>
		</layer>
		<layer id="525" name="Constant_3605" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="6033232" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="526" name="Constant_3604" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="32448" size="8" />
			<output>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="527" name="Constant_3603" type="Const" version="opset1">
			<data element_type="i32" shape="1" offset="6033264" size="4" />
			<output>
				<port id="0" precision="I32">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="528" name="ScatterUpdate_3606" type="ScatterUpdate" version="opset3">
			<input>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
				</port>
				<port id="3" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="529" name="Constant_3607" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="6033232" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="530" name="/model.22/Constant_8" type="Const" version="opset1">
			<data element_type="i64" shape="1" offset="6033256" size="8" />
			<output>
				<port id="0" precision="I64" names="/model.22/Constant_8_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="531" name="/model.22/Mul_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="I64">
					<dim>1</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="I64" names="/model.22/Mul_1_output_0">
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="532" name="ScatterUpdate_3608" type="ScatterUpdate" version="opset3">
			<input>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
				<port id="1" precision="I64">
					<dim>1</dim>
				</port>
				<port id="2" precision="I64">
					<dim>1</dim>
				</port>
				<port id="3" precision="I32">
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="533" name="Constant_3611" type="Const" version="opset1">
			<data element_type="i64" shape="2" offset="6033268" size="16" />
			<output>
				<port id="0" precision="I64">
					<dim>2</dim>
				</port>
			</output>
		</layer>
		<layer id="534" name="/model.22/Slice_1" type="StridedSlice" version="opset1">
			<data begin_mask="1, 0" end_mask="1, 0" new_axis_mask="" shrink_axis_mask="" ellipsis_mask="" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>5040</dim>
				</port>
				<port id="1" precision="I64">
					<dim>2</dim>
				</port>
				<port id="2" precision="I64">
					<dim>2</dim>
				</port>
				<port id="3" precision="I64">
					<dim>2</dim>
				</port>
			</input>
			<output>
				<port id="4" precision="FP32" names="/model.22/Slice_1_output_0">
					<dim>1</dim>
					<dim>2</dim>
					<dim>5040</dim>
				</port>
			</output>
		</layer>
		<layer id="535" name="/model.22/Add_1" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>2</dim>
					<dim>5040</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>2</dim>
					<dim>5040</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/Add_1_output_0">
					<dim>1</dim>
					<dim>2</dim>
					<dim>5040</dim>
				</port>
			</output>
		</layer>
		<layer id="536" name="/model.22/Add_2" type="Add" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>2</dim>
					<dim>5040</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>2</dim>
					<dim>5040</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/Add_2_output_0">
					<dim>1</dim>
					<dim>2</dim>
					<dim>5040</dim>
				</port>
			</output>
		</layer>
		<layer id="537" name="Constant_3954_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 1, 1" offset="6033284" size="2" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="538" name="Constant_3954" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</output>
		</layer>
		<layer id="539" name="/model.22/Div_1" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>2</dim>
					<dim>5040</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>1</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/Div_1_output_0">
					<dim>1</dim>
					<dim>2</dim>
					<dim>5040</dim>
				</port>
			</output>
		</layer>
		<layer id="540" name="/model.22/Sub_1" type="Subtract" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>2</dim>
					<dim>5040</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>2</dim>
					<dim>5040</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/Sub_1_output_0">
					<dim>1</dim>
					<dim>2</dim>
					<dim>5040</dim>
				</port>
			</output>
		</layer>
		<layer id="541" name="/model.22/Concat_4" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>2</dim>
					<dim>5040</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>2</dim>
					<dim>5040</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/Concat_4_output_0">
					<dim>1</dim>
					<dim>4</dim>
					<dim>5040</dim>
				</port>
			</output>
		</layer>
		<layer id="542" name="Constant_3955_compressed" type="Const" version="opset1">
			<data element_type="f16" shape="1, 1, 5040" offset="6033286" size="10080" />
			<output>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>1</dim>
					<dim>5040</dim>
				</port>
			</output>
		</layer>
		<layer id="543" name="Constant_3955" type="Convert" version="opset1">
			<data destination_type="f32" />
			<rt_info>
				<attribute name="decompression" version="0" />
			</rt_info>
			<input>
				<port id="0" precision="FP16">
					<dim>1</dim>
					<dim>1</dim>
					<dim>5040</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>5040</dim>
				</port>
			</output>
		</layer>
		<layer id="544" name="/model.22/Mul_2" type="Multiply" version="opset1">
			<data auto_broadcast="numpy" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>5040</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>1</dim>
					<dim>5040</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="/model.22/Mul_2_output_0">
					<dim>1</dim>
					<dim>4</dim>
					<dim>5040</dim>
				</port>
			</output>
		</layer>
		<layer id="545" name="/model.22/Sigmoid" type="Sigmoid" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>5040</dim>
				</port>
			</input>
			<output>
				<port id="1" precision="FP32" names="/model.22/Sigmoid_output_0">
					<dim>1</dim>
					<dim>4</dim>
					<dim>5040</dim>
				</port>
			</output>
		</layer>
		<layer id="546" name="output0" type="Concat" version="opset1">
			<data axis="1" />
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>5040</dim>
				</port>
				<port id="1" precision="FP32">
					<dim>1</dim>
					<dim>4</dim>
					<dim>5040</dim>
				</port>
			</input>
			<output>
				<port id="2" precision="FP32" names="output0">
					<dim>1</dim>
					<dim>8</dim>
					<dim>5040</dim>
				</port>
			</output>
		</layer>
		<layer id="547" name="output0/sink_port_0" type="Result" version="opset1">
			<input>
				<port id="0" precision="FP32">
					<dim>1</dim>
					<dim>8</dim>
					<dim>5040</dim>
				</port>
			</input>
		</layer>
	</layers>
	<edges>
		<edge from-layer="0" from-port="0" to-layer="5" to-port="0" />
		<edge from-layer="1" from-port="0" to-layer="2" to-port="0" />
		<edge from-layer="2" from-port="1" to-layer="522" to-port="0" />
		<edge from-layer="3" from-port="0" to-layer="4" to-port="0" />
		<edge from-layer="4" from-port="1" to-layer="5" to-port="1" />
		<edge from-layer="5" from-port="2" to-layer="8" to-port="0" />
		<edge from-layer="6" from-port="0" to-layer="7" to-port="0" />
		<edge from-layer="7" from-port="1" to-layer="8" to-port="1" />
		<edge from-layer="8" from-port="2" to-layer="9" to-port="0" />
		<edge from-layer="9" from-port="1" to-layer="12" to-port="0" />
		<edge from-layer="10" from-port="0" to-layer="11" to-port="0" />
		<edge from-layer="11" from-port="1" to-layer="12" to-port="1" />
		<edge from-layer="12" from-port="2" to-layer="15" to-port="0" />
		<edge from-layer="13" from-port="0" to-layer="14" to-port="0" />
		<edge from-layer="14" from-port="1" to-layer="15" to-port="1" />
		<edge from-layer="15" from-port="2" to-layer="16" to-port="0" />
		<edge from-layer="16" from-port="1" to-layer="19" to-port="0" />
		<edge from-layer="17" from-port="0" to-layer="18" to-port="0" />
		<edge from-layer="18" from-port="1" to-layer="19" to-port="1" />
		<edge from-layer="19" from-port="2" to-layer="22" to-port="0" />
		<edge from-layer="20" from-port="0" to-layer="21" to-port="0" />
		<edge from-layer="21" from-port="1" to-layer="22" to-port="1" />
		<edge from-layer="22" from-port="2" to-layer="23" to-port="0" />
		<edge from-layer="23" from-port="1" to-layer="26" to-port="0" />
		<edge from-layer="24" from-port="0" to-layer="26" to-port="1" />
		<edge from-layer="25" from-port="0" to-layer="26" to-port="2" />
		<edge from-layer="26" from-port="4" to-layer="29" to-port="0" />
		<edge from-layer="26" from-port="4" to-layer="41" to-port="0" />
		<edge from-layer="26" from-port="3" to-layer="42" to-port="0" />
		<edge from-layer="26" from-port="4" to-layer="42" to-port="1" />
		<edge from-layer="27" from-port="0" to-layer="28" to-port="0" />
		<edge from-layer="28" from-port="1" to-layer="29" to-port="1" />
		<edge from-layer="29" from-port="2" to-layer="32" to-port="0" />
		<edge from-layer="30" from-port="0" to-layer="31" to-port="0" />
		<edge from-layer="31" from-port="1" to-layer="32" to-port="1" />
		<edge from-layer="32" from-port="2" to-layer="33" to-port="0" />
		<edge from-layer="33" from-port="1" to-layer="36" to-port="0" />
		<edge from-layer="34" from-port="0" to-layer="35" to-port="0" />
		<edge from-layer="35" from-port="1" to-layer="36" to-port="1" />
		<edge from-layer="36" from-port="2" to-layer="39" to-port="0" />
		<edge from-layer="37" from-port="0" to-layer="38" to-port="0" />
		<edge from-layer="38" from-port="1" to-layer="39" to-port="1" />
		<edge from-layer="39" from-port="2" to-layer="40" to-port="0" />
		<edge from-layer="40" from-port="1" to-layer="41" to-port="1" />
		<edge from-layer="41" from-port="2" to-layer="42" to-port="2" />
		<edge from-layer="42" from-port="3" to-layer="45" to-port="0" />
		<edge from-layer="43" from-port="0" to-layer="44" to-port="0" />
		<edge from-layer="44" from-port="1" to-layer="45" to-port="1" />
		<edge from-layer="45" from-port="2" to-layer="48" to-port="0" />
		<edge from-layer="46" from-port="0" to-layer="47" to-port="0" />
		<edge from-layer="47" from-port="1" to-layer="48" to-port="1" />
		<edge from-layer="48" from-port="2" to-layer="49" to-port="0" />
		<edge from-layer="49" from-port="1" to-layer="52" to-port="0" />
		<edge from-layer="50" from-port="0" to-layer="51" to-port="0" />
		<edge from-layer="51" from-port="1" to-layer="52" to-port="1" />
		<edge from-layer="52" from-port="2" to-layer="55" to-port="0" />
		<edge from-layer="53" from-port="0" to-layer="54" to-port="0" />
		<edge from-layer="54" from-port="1" to-layer="55" to-port="1" />
		<edge from-layer="55" from-port="2" to-layer="56" to-port="0" />
		<edge from-layer="56" from-port="1" to-layer="59" to-port="0" />
		<edge from-layer="57" from-port="0" to-layer="58" to-port="0" />
		<edge from-layer="58" from-port="1" to-layer="59" to-port="1" />
		<edge from-layer="59" from-port="2" to-layer="62" to-port="0" />
		<edge from-layer="60" from-port="0" to-layer="61" to-port="0" />
		<edge from-layer="61" from-port="1" to-layer="62" to-port="1" />
		<edge from-layer="62" from-port="2" to-layer="63" to-port="0" />
		<edge from-layer="63" from-port="1" to-layer="66" to-port="0" />
		<edge from-layer="64" from-port="0" to-layer="66" to-port="1" />
		<edge from-layer="65" from-port="0" to-layer="66" to-port="2" />
		<edge from-layer="65" from-port="0" to-layer="263" to-port="2" />
		<edge from-layer="66" from-port="3" to-layer="97" to-port="0" />
		<edge from-layer="66" from-port="4" to-layer="69" to-port="0" />
		<edge from-layer="66" from-port="4" to-layer="81" to-port="0" />
		<edge from-layer="66" from-port="4" to-layer="97" to-port="1" />
		<edge from-layer="67" from-port="0" to-layer="68" to-port="0" />
		<edge from-layer="68" from-port="1" to-layer="69" to-port="1" />
		<edge from-layer="69" from-port="2" to-layer="72" to-port="0" />
		<edge from-layer="70" from-port="0" to-layer="71" to-port="0" />
		<edge from-layer="71" from-port="1" to-layer="72" to-port="1" />
		<edge from-layer="72" from-port="2" to-layer="73" to-port="0" />
		<edge from-layer="73" from-port="1" to-layer="76" to-port="0" />
		<edge from-layer="74" from-port="0" to-layer="75" to-port="0" />
		<edge from-layer="75" from-port="1" to-layer="76" to-port="1" />
		<edge from-layer="76" from-port="2" to-layer="79" to-port="0" />
		<edge from-layer="77" from-port="0" to-layer="78" to-port="0" />
		<edge from-layer="78" from-port="1" to-layer="79" to-port="1" />
		<edge from-layer="79" from-port="2" to-layer="80" to-port="0" />
		<edge from-layer="80" from-port="1" to-layer="81" to-port="1" />
		<edge from-layer="81" from-port="2" to-layer="84" to-port="0" />
		<edge from-layer="81" from-port="2" to-layer="97" to-port="2" />
		<edge from-layer="81" from-port="2" to-layer="96" to-port="0" />
		<edge from-layer="82" from-port="0" to-layer="83" to-port="0" />
		<edge from-layer="83" from-port="1" to-layer="84" to-port="1" />
		<edge from-layer="84" from-port="2" to-layer="87" to-port="0" />
		<edge from-layer="85" from-port="0" to-layer="86" to-port="0" />
		<edge from-layer="86" from-port="1" to-layer="87" to-port="1" />
		<edge from-layer="87" from-port="2" to-layer="88" to-port="0" />
		<edge from-layer="88" from-port="1" to-layer="91" to-port="0" />
		<edge from-layer="89" from-port="0" to-layer="90" to-port="0" />
		<edge from-layer="90" from-port="1" to-layer="91" to-port="1" />
		<edge from-layer="91" from-port="2" to-layer="94" to-port="0" />
		<edge from-layer="92" from-port="0" to-layer="93" to-port="0" />
		<edge from-layer="93" from-port="1" to-layer="94" to-port="1" />
		<edge from-layer="94" from-port="2" to-layer="95" to-port="0" />
		<edge from-layer="95" from-port="1" to-layer="96" to-port="1" />
		<edge from-layer="96" from-port="2" to-layer="97" to-port="3" />
		<edge from-layer="97" from-port="4" to-layer="100" to-port="0" />
		<edge from-layer="98" from-port="0" to-layer="99" to-port="0" />
		<edge from-layer="99" from-port="1" to-layer="100" to-port="1" />
		<edge from-layer="100" from-port="2" to-layer="103" to-port="0" />
		<edge from-layer="101" from-port="0" to-layer="102" to-port="0" />
		<edge from-layer="102" from-port="1" to-layer="103" to-port="1" />
		<edge from-layer="103" from-port="2" to-layer="104" to-port="0" />
		<edge from-layer="104" from-port="1" to-layer="107" to-port="0" />
		<edge from-layer="104" from-port="1" to-layer="254" to-port="1" />
		<edge from-layer="105" from-port="0" to-layer="106" to-port="0" />
		<edge from-layer="106" from-port="1" to-layer="107" to-port="1" />
		<edge from-layer="107" from-port="2" to-layer="110" to-port="0" />
		<edge from-layer="108" from-port="0" to-layer="109" to-port="0" />
		<edge from-layer="109" from-port="1" to-layer="110" to-port="1" />
		<edge from-layer="110" from-port="2" to-layer="111" to-port="0" />
		<edge from-layer="111" from-port="1" to-layer="114" to-port="0" />
		<edge from-layer="112" from-port="0" to-layer="113" to-port="0" />
		<edge from-layer="113" from-port="1" to-layer="114" to-port="1" />
		<edge from-layer="114" from-port="2" to-layer="117" to-port="0" />
		<edge from-layer="115" from-port="0" to-layer="116" to-port="0" />
		<edge from-layer="116" from-port="1" to-layer="117" to-port="1" />
		<edge from-layer="117" from-port="2" to-layer="118" to-port="0" />
		<edge from-layer="118" from-port="1" to-layer="121" to-port="0" />
		<edge from-layer="119" from-port="0" to-layer="121" to-port="1" />
		<edge from-layer="120" from-port="0" to-layer="121" to-port="2" />
		<edge from-layer="120" from-port="0" to-layer="345" to-port="2" />
		<edge from-layer="120" from-port="0" to-layer="229" to-port="2" />
		<edge from-layer="121" from-port="4" to-layer="124" to-port="0" />
		<edge from-layer="121" from-port="4" to-layer="136" to-port="0" />
		<edge from-layer="121" from-port="3" to-layer="152" to-port="0" />
		<edge from-layer="121" from-port="4" to-layer="152" to-port="1" />
		<edge from-layer="122" from-port="0" to-layer="123" to-port="0" />
		<edge from-layer="123" from-port="1" to-layer="124" to-port="1" />
		<edge from-layer="124" from-port="2" to-layer="127" to-port="0" />
		<edge from-layer="125" from-port="0" to-layer="126" to-port="0" />
		<edge from-layer="126" from-port="1" to-layer="127" to-port="1" />
		<edge from-layer="127" from-port="2" to-layer="128" to-port="0" />
		<edge from-layer="128" from-port="1" to-layer="131" to-port="0" />
		<edge from-layer="129" from-port="0" to-layer="130" to-port="0" />
		<edge from-layer="130" from-port="1" to-layer="131" to-port="1" />
		<edge from-layer="131" from-port="2" to-layer="134" to-port="0" />
		<edge from-layer="132" from-port="0" to-layer="133" to-port="0" />
		<edge from-layer="133" from-port="1" to-layer="134" to-port="1" />
		<edge from-layer="134" from-port="2" to-layer="135" to-port="0" />
		<edge from-layer="135" from-port="1" to-layer="136" to-port="1" />
		<edge from-layer="136" from-port="2" to-layer="151" to-port="0" />
		<edge from-layer="136" from-port="2" to-layer="152" to-port="2" />
		<edge from-layer="136" from-port="2" to-layer="139" to-port="0" />
		<edge from-layer="137" from-port="0" to-layer="138" to-port="0" />
		<edge from-layer="138" from-port="1" to-layer="139" to-port="1" />
		<edge from-layer="139" from-port="2" to-layer="142" to-port="0" />
		<edge from-layer="140" from-port="0" to-layer="141" to-port="0" />
		<edge from-layer="141" from-port="1" to-layer="142" to-port="1" />
		<edge from-layer="142" from-port="2" to-layer="143" to-port="0" />
		<edge from-layer="143" from-port="1" to-layer="146" to-port="0" />
		<edge from-layer="144" from-port="0" to-layer="145" to-port="0" />
		<edge from-layer="145" from-port="1" to-layer="146" to-port="1" />
		<edge from-layer="146" from-port="2" to-layer="149" to-port="0" />
		<edge from-layer="147" from-port="0" to-layer="148" to-port="0" />
		<edge from-layer="148" from-port="1" to-layer="149" to-port="1" />
		<edge from-layer="149" from-port="2" to-layer="150" to-port="0" />
		<edge from-layer="150" from-port="1" to-layer="151" to-port="1" />
		<edge from-layer="151" from-port="2" to-layer="152" to-port="3" />
		<edge from-layer="152" from-port="4" to-layer="155" to-port="0" />
		<edge from-layer="153" from-port="0" to-layer="154" to-port="0" />
		<edge from-layer="154" from-port="1" to-layer="155" to-port="1" />
		<edge from-layer="155" from-port="2" to-layer="158" to-port="0" />
		<edge from-layer="156" from-port="0" to-layer="157" to-port="0" />
		<edge from-layer="157" from-port="1" to-layer="158" to-port="1" />
		<edge from-layer="158" from-port="2" to-layer="159" to-port="0" />
		<edge from-layer="159" from-port="1" to-layer="162" to-port="0" />
		<edge from-layer="159" from-port="1" to-layer="220" to-port="1" />
		<edge from-layer="160" from-port="0" to-layer="161" to-port="0" />
		<edge from-layer="161" from-port="1" to-layer="162" to-port="1" />
		<edge from-layer="162" from-port="2" to-layer="165" to-port="0" />
		<edge from-layer="163" from-port="0" to-layer="164" to-port="0" />
		<edge from-layer="164" from-port="1" to-layer="165" to-port="1" />
		<edge from-layer="165" from-port="2" to-layer="166" to-port="0" />
		<edge from-layer="166" from-port="1" to-layer="169" to-port="0" />
		<edge from-layer="167" from-port="0" to-layer="168" to-port="0" />
		<edge from-layer="168" from-port="1" to-layer="169" to-port="1" />
		<edge from-layer="169" from-port="2" to-layer="172" to-port="0" />
		<edge from-layer="170" from-port="0" to-layer="171" to-port="0" />
		<edge from-layer="171" from-port="1" to-layer="172" to-port="1" />
		<edge from-layer="172" from-port="2" to-layer="173" to-port="0" />
		<edge from-layer="173" from-port="1" to-layer="176" to-port="0" />
		<edge from-layer="174" from-port="0" to-layer="176" to-port="1" />
		<edge from-layer="175" from-port="0" to-layer="176" to-port="2" />
		<edge from-layer="175" from-port="0" to-layer="427" to-port="2" />
		<edge from-layer="176" from-port="4" to-layer="179" to-port="0" />
		<edge from-layer="176" from-port="4" to-layer="191" to-port="0" />
		<edge from-layer="176" from-port="3" to-layer="192" to-port="0" />
		<edge from-layer="176" from-port="4" to-layer="192" to-port="1" />
		<edge from-layer="177" from-port="0" to-layer="178" to-port="0" />
		<edge from-layer="178" from-port="1" to-layer="179" to-port="1" />
		<edge from-layer="179" from-port="2" to-layer="182" to-port="0" />
		<edge from-layer="180" from-port="0" to-layer="181" to-port="0" />
		<edge from-layer="181" from-port="1" to-layer="182" to-port="1" />
		<edge from-layer="182" from-port="2" to-layer="183" to-port="0" />
		<edge from-layer="183" from-port="1" to-layer="186" to-port="0" />
		<edge from-layer="184" from-port="0" to-layer="185" to-port="0" />
		<edge from-layer="185" from-port="1" to-layer="186" to-port="1" />
		<edge from-layer="186" from-port="2" to-layer="189" to-port="0" />
		<edge from-layer="187" from-port="0" to-layer="188" to-port="0" />
		<edge from-layer="188" from-port="1" to-layer="189" to-port="1" />
		<edge from-layer="189" from-port="2" to-layer="190" to-port="0" />
		<edge from-layer="190" from-port="1" to-layer="191" to-port="1" />
		<edge from-layer="191" from-port="2" to-layer="192" to-port="2" />
		<edge from-layer="192" from-port="3" to-layer="195" to-port="0" />
		<edge from-layer="193" from-port="0" to-layer="194" to-port="0" />
		<edge from-layer="194" from-port="1" to-layer="195" to-port="1" />
		<edge from-layer="195" from-port="2" to-layer="198" to-port="0" />
		<edge from-layer="196" from-port="0" to-layer="197" to-port="0" />
		<edge from-layer="197" from-port="1" to-layer="198" to-port="1" />
		<edge from-layer="198" from-port="2" to-layer="199" to-port="0" />
		<edge from-layer="199" from-port="1" to-layer="202" to-port="0" />
		<edge from-layer="200" from-port="0" to-layer="201" to-port="0" />
		<edge from-layer="201" from-port="1" to-layer="202" to-port="1" />
		<edge from-layer="202" from-port="2" to-layer="205" to-port="0" />
		<edge from-layer="203" from-port="0" to-layer="204" to-port="0" />
		<edge from-layer="204" from-port="1" to-layer="205" to-port="1" />
		<edge from-layer="205" from-port="2" to-layer="206" to-port="0" />
		<edge from-layer="206" from-port="1" to-layer="207" to-port="0" />
		<edge from-layer="206" from-port="1" to-layer="210" to-port="0" />
		<edge from-layer="207" from-port="1" to-layer="208" to-port="0" />
		<edge from-layer="207" from-port="1" to-layer="210" to-port="1" />
		<edge from-layer="208" from-port="1" to-layer="209" to-port="0" />
		<edge from-layer="208" from-port="1" to-layer="210" to-port="2" />
		<edge from-layer="209" from-port="1" to-layer="210" to-port="3" />
		<edge from-layer="210" from-port="4" to-layer="213" to-port="0" />
		<edge from-layer="211" from-port="0" to-layer="212" to-port="0" />
		<edge from-layer="212" from-port="1" to-layer="213" to-port="1" />
		<edge from-layer="213" from-port="2" to-layer="216" to-port="0" />
		<edge from-layer="214" from-port="0" to-layer="215" to-port="0" />
		<edge from-layer="215" from-port="1" to-layer="216" to-port="1" />
		<edge from-layer="216" from-port="2" to-layer="217" to-port="0" />
		<edge from-layer="217" from-port="1" to-layer="418" to-port="1" />
		<edge from-layer="217" from-port="1" to-layer="219" to-port="0" />
		<edge from-layer="218" from-port="0" to-layer="219" to-port="1" />
		<edge from-layer="219" from-port="2" to-layer="220" to-port="0" />
		<edge from-layer="220" from-port="2" to-layer="223" to-port="0" />
		<edge from-layer="221" from-port="0" to-layer="222" to-port="0" />
		<edge from-layer="222" from-port="1" to-layer="223" to-port="1" />
		<edge from-layer="223" from-port="2" to-layer="226" to-port="0" />
		<edge from-layer="224" from-port="0" to-layer="225" to-port="0" />
		<edge from-layer="225" from-port="1" to-layer="226" to-port="1" />
		<edge from-layer="226" from-port="2" to-layer="227" to-port="0" />
		<edge from-layer="227" from-port="1" to-layer="229" to-port="0" />
		<edge from-layer="228" from-port="0" to-layer="229" to-port="1" />
		<edge from-layer="229" from-port="4" to-layer="232" to-port="0" />
		<edge from-layer="229" from-port="3" to-layer="244" to-port="0" />
		<edge from-layer="229" from-port="4" to-layer="244" to-port="1" />
		<edge from-layer="230" from-port="0" to-layer="231" to-port="0" />
		<edge from-layer="231" from-port="1" to-layer="232" to-port="1" />
		<edge from-layer="232" from-port="2" to-layer="235" to-port="0" />
		<edge from-layer="233" from-port="0" to-layer="234" to-port="0" />
		<edge from-layer="234" from-port="1" to-layer="235" to-port="1" />
		<edge from-layer="235" from-port="2" to-layer="236" to-port="0" />
		<edge from-layer="236" from-port="1" to-layer="239" to-port="0" />
		<edge from-layer="237" from-port="0" to-layer="238" to-port="0" />
		<edge from-layer="238" from-port="1" to-layer="239" to-port="1" />
		<edge from-layer="239" from-port="2" to-layer="242" to-port="0" />
		<edge from-layer="240" from-port="0" to-layer="241" to-port="0" />
		<edge from-layer="241" from-port="1" to-layer="242" to-port="1" />
		<edge from-layer="242" from-port="2" to-layer="243" to-port="0" />
		<edge from-layer="243" from-port="1" to-layer="244" to-port="2" />
		<edge from-layer="244" from-port="3" to-layer="247" to-port="0" />
		<edge from-layer="245" from-port="0" to-layer="246" to-port="0" />
		<edge from-layer="246" from-port="1" to-layer="247" to-port="1" />
		<edge from-layer="247" from-port="2" to-layer="250" to-port="0" />
		<edge from-layer="248" from-port="0" to-layer="249" to-port="0" />
		<edge from-layer="249" from-port="1" to-layer="250" to-port="1" />
		<edge from-layer="250" from-port="2" to-layer="251" to-port="0" />
		<edge from-layer="251" from-port="1" to-layer="253" to-port="0" />
		<edge from-layer="251" from-port="1" to-layer="336" to-port="1" />
		<edge from-layer="252" from-port="0" to-layer="253" to-port="1" />
		<edge from-layer="253" from-port="2" to-layer="254" to-port="0" />
		<edge from-layer="254" from-port="2" to-layer="257" to-port="0" />
		<edge from-layer="255" from-port="0" to-layer="256" to-port="0" />
		<edge from-layer="256" from-port="1" to-layer="257" to-port="1" />
		<edge from-layer="257" from-port="2" to-layer="260" to-port="0" />
		<edge from-layer="258" from-port="0" to-layer="259" to-port="0" />
		<edge from-layer="259" from-port="1" to-layer="260" to-port="1" />
		<edge from-layer="260" from-port="2" to-layer="261" to-port="0" />
		<edge from-layer="261" from-port="1" to-layer="263" to-port="0" />
		<edge from-layer="262" from-port="0" to-layer="263" to-port="1" />
		<edge from-layer="263" from-port="4" to-layer="266" to-port="0" />
		<edge from-layer="263" from-port="3" to-layer="278" to-port="0" />
		<edge from-layer="263" from-port="4" to-layer="278" to-port="1" />
		<edge from-layer="264" from-port="0" to-layer="265" to-port="0" />
		<edge from-layer="265" from-port="1" to-layer="266" to-port="1" />
		<edge from-layer="266" from-port="2" to-layer="269" to-port="0" />
		<edge from-layer="267" from-port="0" to-layer="268" to-port="0" />
		<edge from-layer="268" from-port="1" to-layer="269" to-port="1" />
		<edge from-layer="269" from-port="2" to-layer="270" to-port="0" />
		<edge from-layer="270" from-port="1" to-layer="273" to-port="0" />
		<edge from-layer="271" from-port="0" to-layer="272" to-port="0" />
		<edge from-layer="272" from-port="1" to-layer="273" to-port="1" />
		<edge from-layer="273" from-port="2" to-layer="276" to-port="0" />
		<edge from-layer="274" from-port="0" to-layer="275" to-port="0" />
		<edge from-layer="275" from-port="1" to-layer="276" to-port="1" />
		<edge from-layer="276" from-port="2" to-layer="277" to-port="0" />
		<edge from-layer="277" from-port="1" to-layer="278" to-port="2" />
		<edge from-layer="278" from-port="3" to-layer="281" to-port="0" />
		<edge from-layer="279" from-port="0" to-layer="280" to-port="0" />
		<edge from-layer="280" from-port="1" to-layer="281" to-port="1" />
		<edge from-layer="281" from-port="2" to-layer="284" to-port="0" />
		<edge from-layer="282" from-port="0" to-layer="283" to-port="0" />
		<edge from-layer="283" from-port="1" to-layer="284" to-port="1" />
		<edge from-layer="284" from-port="2" to-layer="285" to-port="0" />
		<edge from-layer="285" from-port="1" to-layer="288" to-port="0" />
		<edge from-layer="285" from-port="1" to-layer="308" to-port="0" />
		<edge from-layer="285" from-port="1" to-layer="331" to-port="0" />
		<edge from-layer="286" from-port="0" to-layer="287" to-port="0" />
		<edge from-layer="287" from-port="1" to-layer="288" to-port="1" />
		<edge from-layer="288" from-port="2" to-layer="291" to-port="0" />
		<edge from-layer="289" from-port="0" to-layer="290" to-port="0" />
		<edge from-layer="290" from-port="1" to-layer="291" to-port="1" />
		<edge from-layer="291" from-port="2" to-layer="292" to-port="0" />
		<edge from-layer="292" from-port="1" to-layer="295" to-port="0" />
		<edge from-layer="293" from-port="0" to-layer="294" to-port="0" />
		<edge from-layer="294" from-port="1" to-layer="295" to-port="1" />
		<edge from-layer="295" from-port="2" to-layer="298" to-port="0" />
		<edge from-layer="296" from-port="0" to-layer="297" to-port="0" />
		<edge from-layer="297" from-port="1" to-layer="298" to-port="1" />
		<edge from-layer="298" from-port="2" to-layer="299" to-port="0" />
		<edge from-layer="299" from-port="1" to-layer="302" to-port="0" />
		<edge from-layer="300" from-port="0" to-layer="301" to-port="0" />
		<edge from-layer="301" from-port="1" to-layer="302" to-port="1" />
		<edge from-layer="302" from-port="2" to-layer="305" to-port="0" />
		<edge from-layer="303" from-port="0" to-layer="304" to-port="0" />
		<edge from-layer="304" from-port="1" to-layer="305" to-port="1" />
		<edge from-layer="305" from-port="2" to-layer="326" to-port="0" />
		<edge from-layer="306" from-port="0" to-layer="307" to-port="0" />
		<edge from-layer="307" from-port="1" to-layer="308" to-port="1" />
		<edge from-layer="308" from-port="2" to-layer="311" to-port="0" />
		<edge from-layer="309" from-port="0" to-layer="310" to-port="0" />
		<edge from-layer="310" from-port="1" to-layer="311" to-port="1" />
		<edge from-layer="311" from-port="2" to-layer="312" to-port="0" />
		<edge from-layer="312" from-port="1" to-layer="315" to-port="0" />
		<edge from-layer="313" from-port="0" to-layer="314" to-port="0" />
		<edge from-layer="314" from-port="1" to-layer="315" to-port="1" />
		<edge from-layer="315" from-port="2" to-layer="318" to-port="0" />
		<edge from-layer="316" from-port="0" to-layer="317" to-port="0" />
		<edge from-layer="317" from-port="1" to-layer="318" to-port="1" />
		<edge from-layer="318" from-port="2" to-layer="319" to-port="0" />
		<edge from-layer="319" from-port="1" to-layer="322" to-port="0" />
		<edge from-layer="320" from-port="0" to-layer="321" to-port="0" />
		<edge from-layer="321" from-port="1" to-layer="322" to-port="1" />
		<edge from-layer="322" from-port="2" to-layer="325" to-port="0" />
		<edge from-layer="323" from-port="0" to-layer="324" to-port="0" />
		<edge from-layer="324" from-port="1" to-layer="325" to-port="1" />
		<edge from-layer="325" from-port="2" to-layer="326" to-port="1" />
		<edge from-layer="326" from-port="2" to-layer="328" to-port="0" />
		<edge from-layer="327" from-port="0" to-layer="328" to-port="1" />
		<edge from-layer="328" from-port="2" to-layer="493" to-port="0" />
		<edge from-layer="329" from-port="0" to-layer="330" to-port="0" />
		<edge from-layer="330" from-port="1" to-layer="331" to-port="1" />
		<edge from-layer="331" from-port="2" to-layer="334" to-port="0" />
		<edge from-layer="332" from-port="0" to-layer="333" to-port="0" />
		<edge from-layer="333" from-port="1" to-layer="334" to-port="1" />
		<edge from-layer="334" from-port="2" to-layer="335" to-port="0" />
		<edge from-layer="335" from-port="1" to-layer="336" to-port="0" />
		<edge from-layer="336" from-port="2" to-layer="339" to-port="0" />
		<edge from-layer="337" from-port="0" to-layer="338" to-port="0" />
		<edge from-layer="338" from-port="1" to-layer="339" to-port="1" />
		<edge from-layer="339" from-port="2" to-layer="342" to-port="0" />
		<edge from-layer="340" from-port="0" to-layer="341" to-port="0" />
		<edge from-layer="341" from-port="1" to-layer="342" to-port="1" />
		<edge from-layer="342" from-port="2" to-layer="343" to-port="0" />
		<edge from-layer="343" from-port="1" to-layer="345" to-port="0" />
		<edge from-layer="344" from-port="0" to-layer="345" to-port="1" />
		<edge from-layer="345" from-port="4" to-layer="348" to-port="0" />
		<edge from-layer="345" from-port="3" to-layer="360" to-port="0" />
		<edge from-layer="345" from-port="4" to-layer="360" to-port="1" />
		<edge from-layer="346" from-port="0" to-layer="347" to-port="0" />
		<edge from-layer="347" from-port="1" to-layer="348" to-port="1" />
		<edge from-layer="348" from-port="2" to-layer="351" to-port="0" />
		<edge from-layer="349" from-port="0" to-layer="350" to-port="0" />
		<edge from-layer="350" from-port="1" to-layer="351" to-port="1" />
		<edge from-layer="351" from-port="2" to-layer="352" to-port="0" />
		<edge from-layer="352" from-port="1" to-layer="355" to-port="0" />
		<edge from-layer="353" from-port="0" to-layer="354" to-port="0" />
		<edge from-layer="354" from-port="1" to-layer="355" to-port="1" />
		<edge from-layer="355" from-port="2" to-layer="358" to-port="0" />
		<edge from-layer="356" from-port="0" to-layer="357" to-port="0" />
		<edge from-layer="357" from-port="1" to-layer="358" to-port="1" />
		<edge from-layer="358" from-port="2" to-layer="359" to-port="0" />
		<edge from-layer="359" from-port="1" to-layer="360" to-port="2" />
		<edge from-layer="360" from-port="3" to-layer="363" to-port="0" />
		<edge from-layer="361" from-port="0" to-layer="362" to-port="0" />
		<edge from-layer="362" from-port="1" to-layer="363" to-port="1" />
		<edge from-layer="363" from-port="2" to-layer="366" to-port="0" />
		<edge from-layer="364" from-port="0" to-layer="365" to-port="0" />
		<edge from-layer="365" from-port="1" to-layer="366" to-port="1" />
		<edge from-layer="366" from-port="2" to-layer="367" to-port="0" />
		<edge from-layer="367" from-port="1" to-layer="390" to-port="0" />
		<edge from-layer="367" from-port="1" to-layer="370" to-port="0" />
		<edge from-layer="367" from-port="1" to-layer="413" to-port="0" />
		<edge from-layer="368" from-port="0" to-layer="369" to-port="0" />
		<edge from-layer="369" from-port="1" to-layer="370" to-port="1" />
		<edge from-layer="370" from-port="2" to-layer="373" to-port="0" />
		<edge from-layer="371" from-port="0" to-layer="372" to-port="0" />
		<edge from-layer="372" from-port="1" to-layer="373" to-port="1" />
		<edge from-layer="373" from-port="2" to-layer="374" to-port="0" />
		<edge from-layer="374" from-port="1" to-layer="377" to-port="0" />
		<edge from-layer="375" from-port="0" to-layer="376" to-port="0" />
		<edge from-layer="376" from-port="1" to-layer="377" to-port="1" />
		<edge from-layer="377" from-port="2" to-layer="380" to-port="0" />
		<edge from-layer="378" from-port="0" to-layer="379" to-port="0" />
		<edge from-layer="379" from-port="1" to-layer="380" to-port="1" />
		<edge from-layer="380" from-port="2" to-layer="381" to-port="0" />
		<edge from-layer="381" from-port="1" to-layer="384" to-port="0" />
		<edge from-layer="382" from-port="0" to-layer="383" to-port="0" />
		<edge from-layer="383" from-port="1" to-layer="384" to-port="1" />
		<edge from-layer="384" from-port="2" to-layer="387" to-port="0" />
		<edge from-layer="385" from-port="0" to-layer="386" to-port="0" />
		<edge from-layer="386" from-port="1" to-layer="387" to-port="1" />
		<edge from-layer="387" from-port="2" to-layer="408" to-port="0" />
		<edge from-layer="388" from-port="0" to-layer="389" to-port="0" />
		<edge from-layer="389" from-port="1" to-layer="390" to-port="1" />
		<edge from-layer="390" from-port="2" to-layer="393" to-port="0" />
		<edge from-layer="391" from-port="0" to-layer="392" to-port="0" />
		<edge from-layer="392" from-port="1" to-layer="393" to-port="1" />
		<edge from-layer="393" from-port="2" to-layer="394" to-port="0" />
		<edge from-layer="394" from-port="1" to-layer="397" to-port="0" />
		<edge from-layer="395" from-port="0" to-layer="396" to-port="0" />
		<edge from-layer="396" from-port="1" to-layer="397" to-port="1" />
		<edge from-layer="397" from-port="2" to-layer="400" to-port="0" />
		<edge from-layer="398" from-port="0" to-layer="399" to-port="0" />
		<edge from-layer="399" from-port="1" to-layer="400" to-port="1" />
		<edge from-layer="400" from-port="2" to-layer="401" to-port="0" />
		<edge from-layer="401" from-port="1" to-layer="404" to-port="0" />
		<edge from-layer="402" from-port="0" to-layer="403" to-port="0" />
		<edge from-layer="403" from-port="1" to-layer="404" to-port="1" />
		<edge from-layer="404" from-port="2" to-layer="407" to-port="0" />
		<edge from-layer="405" from-port="0" to-layer="406" to-port="0" />
		<edge from-layer="406" from-port="1" to-layer="407" to-port="1" />
		<edge from-layer="407" from-port="2" to-layer="408" to-port="1" />
		<edge from-layer="408" from-port="2" to-layer="410" to-port="0" />
		<edge from-layer="409" from-port="0" to-layer="410" to-port="1" />
		<edge from-layer="410" from-port="2" to-layer="493" to-port="1" />
		<edge from-layer="411" from-port="0" to-layer="412" to-port="0" />
		<edge from-layer="412" from-port="1" to-layer="413" to-port="1" />
		<edge from-layer="413" from-port="2" to-layer="416" to-port="0" />
		<edge from-layer="414" from-port="0" to-layer="415" to-port="0" />
		<edge from-layer="415" from-port="1" to-layer="416" to-port="1" />
		<edge from-layer="416" from-port="2" to-layer="417" to-port="0" />
		<edge from-layer="417" from-port="1" to-layer="418" to-port="0" />
		<edge from-layer="418" from-port="2" to-layer="421" to-port="0" />
		<edge from-layer="419" from-port="0" to-layer="420" to-port="0" />
		<edge from-layer="420" from-port="1" to-layer="421" to-port="1" />
		<edge from-layer="421" from-port="2" to-layer="424" to-port="0" />
		<edge from-layer="422" from-port="0" to-layer="423" to-port="0" />
		<edge from-layer="423" from-port="1" to-layer="424" to-port="1" />
		<edge from-layer="424" from-port="2" to-layer="425" to-port="0" />
		<edge from-layer="425" from-port="1" to-layer="427" to-port="0" />
		<edge from-layer="426" from-port="0" to-layer="427" to-port="1" />
		<edge from-layer="427" from-port="4" to-layer="430" to-port="0" />
		<edge from-layer="427" from-port="3" to-layer="442" to-port="0" />
		<edge from-layer="427" from-port="4" to-layer="442" to-port="1" />
		<edge from-layer="428" from-port="0" to-layer="429" to-port="0" />
		<edge from-layer="429" from-port="1" to-layer="430" to-port="1" />
		<edge from-layer="430" from-port="2" to-layer="433" to-port="0" />
		<edge from-layer="431" from-port="0" to-layer="432" to-port="0" />
		<edge from-layer="432" from-port="1" to-layer="433" to-port="1" />
		<edge from-layer="433" from-port="2" to-layer="434" to-port="0" />
		<edge from-layer="434" from-port="1" to-layer="437" to-port="0" />
		<edge from-layer="435" from-port="0" to-layer="436" to-port="0" />
		<edge from-layer="436" from-port="1" to-layer="437" to-port="1" />
		<edge from-layer="437" from-port="2" to-layer="440" to-port="0" />
		<edge from-layer="438" from-port="0" to-layer="439" to-port="0" />
		<edge from-layer="439" from-port="1" to-layer="440" to-port="1" />
		<edge from-layer="440" from-port="2" to-layer="441" to-port="0" />
		<edge from-layer="441" from-port="1" to-layer="442" to-port="2" />
		<edge from-layer="442" from-port="3" to-layer="445" to-port="0" />
		<edge from-layer="443" from-port="0" to-layer="444" to-port="0" />
		<edge from-layer="444" from-port="1" to-layer="445" to-port="1" />
		<edge from-layer="445" from-port="2" to-layer="448" to-port="0" />
		<edge from-layer="446" from-port="0" to-layer="447" to-port="0" />
		<edge from-layer="447" from-port="1" to-layer="448" to-port="1" />
		<edge from-layer="448" from-port="2" to-layer="449" to-port="0" />
		<edge from-layer="449" from-port="1" to-layer="452" to-port="0" />
		<edge from-layer="449" from-port="1" to-layer="472" to-port="0" />
		<edge from-layer="450" from-port="0" to-layer="451" to-port="0" />
		<edge from-layer="451" from-port="1" to-layer="452" to-port="1" />
		<edge from-layer="452" from-port="2" to-layer="455" to-port="0" />
		<edge from-layer="453" from-port="0" to-layer="454" to-port="0" />
		<edge from-layer="454" from-port="1" to-layer="455" to-port="1" />
		<edge from-layer="455" from-port="2" to-layer="456" to-port="0" />
		<edge from-layer="456" from-port="1" to-layer="459" to-port="0" />
		<edge from-layer="457" from-port="0" to-layer="458" to-port="0" />
		<edge from-layer="458" from-port="1" to-layer="459" to-port="1" />
		<edge from-layer="459" from-port="2" to-layer="462" to-port="0" />
		<edge from-layer="460" from-port="0" to-layer="461" to-port="0" />
		<edge from-layer="461" from-port="1" to-layer="462" to-port="1" />
		<edge from-layer="462" from-port="2" to-layer="463" to-port="0" />
		<edge from-layer="463" from-port="1" to-layer="466" to-port="0" />
		<edge from-layer="464" from-port="0" to-layer="465" to-port="0" />
		<edge from-layer="465" from-port="1" to-layer="466" to-port="1" />
		<edge from-layer="466" from-port="2" to-layer="469" to-port="0" />
		<edge from-layer="467" from-port="0" to-layer="468" to-port="0" />
		<edge from-layer="468" from-port="1" to-layer="469" to-port="1" />
		<edge from-layer="469" from-port="2" to-layer="490" to-port="0" />
		<edge from-layer="470" from-port="0" to-layer="471" to-port="0" />
		<edge from-layer="471" from-port="1" to-layer="472" to-port="1" />
		<edge from-layer="472" from-port="2" to-layer="475" to-port="0" />
		<edge from-layer="473" from-port="0" to-layer="474" to-port="0" />
		<edge from-layer="474" from-port="1" to-layer="475" to-port="1" />
		<edge from-layer="475" from-port="2" to-layer="476" to-port="0" />
		<edge from-layer="476" from-port="1" to-layer="479" to-port="0" />
		<edge from-layer="477" from-port="0" to-layer="478" to-port="0" />
		<edge from-layer="478" from-port="1" to-layer="479" to-port="1" />
		<edge from-layer="479" from-port="2" to-layer="482" to-port="0" />
		<edge from-layer="480" from-port="0" to-layer="481" to-port="0" />
		<edge from-layer="481" from-port="1" to-layer="482" to-port="1" />
		<edge from-layer="482" from-port="2" to-layer="483" to-port="0" />
		<edge from-layer="483" from-port="1" to-layer="486" to-port="0" />
		<edge from-layer="484" from-port="0" to-layer="485" to-port="0" />
		<edge from-layer="485" from-port="1" to-layer="486" to-port="1" />
		<edge from-layer="486" from-port="2" to-layer="489" to-port="0" />
		<edge from-layer="487" from-port="0" to-layer="488" to-port="0" />
		<edge from-layer="488" from-port="1" to-layer="489" to-port="1" />
		<edge from-layer="489" from-port="2" to-layer="490" to-port="1" />
		<edge from-layer="490" from-port="2" to-layer="492" to-port="0" />
		<edge from-layer="491" from-port="0" to-layer="492" to-port="1" />
		<edge from-layer="492" from-port="2" to-layer="493" to-port="2" />
		<edge from-layer="493" from-port="3" to-layer="496" to-port="0" />
		<edge from-layer="494" from-port="0" to-layer="496" to-port="1" />
		<edge from-layer="495" from-port="0" to-layer="496" to-port="2" />
		<edge from-layer="496" from-port="3" to-layer="498" to-port="0" />
		<edge from-layer="496" from-port="4" to-layer="545" to-port="0" />
		<edge from-layer="497" from-port="0" to-layer="498" to-port="1" />
		<edge from-layer="498" from-port="2" to-layer="500" to-port="0" />
		<edge from-layer="499" from-port="0" to-layer="500" to-port="1" />
		<edge from-layer="500" from-port="2" to-layer="501" to-port="0" />
		<edge from-layer="501" from-port="1" to-layer="504" to-port="0" />
		<edge from-layer="502" from-port="0" to-layer="503" to-port="0" />
		<edge from-layer="503" from-port="1" to-layer="504" to-port="1" />
		<edge from-layer="504" from-port="2" to-layer="506" to-port="0" />
		<edge from-layer="505" from-port="0" to-layer="506" to-port="1" />
		<edge from-layer="506" from-port="2" to-layer="534" to-port="0" />
		<edge from-layer="506" from-port="2" to-layer="521" to-port="0" />
		<edge from-layer="506" from-port="2" to-layer="510" to-port="0" />
		<edge from-layer="507" from-port="0" to-layer="521" to-port="1" />
		<edge from-layer="508" from-port="0" to-layer="519" to-port="0" />
		<edge from-layer="509" from-port="0" to-layer="519" to-port="1" />
		<edge from-layer="510" from-port="1" to-layer="513" to-port="0" />
		<edge from-layer="511" from-port="0" to-layer="513" to-port="1" />
		<edge from-layer="512" from-port="0" to-layer="513" to-port="2" />
		<edge from-layer="513" from-port="3" to-layer="515" to-port="0" />
		<edge from-layer="514" from-port="0" to-layer="515" to-port="1" />
		<edge from-layer="515" from-port="2" to-layer="517" to-port="0" />
		<edge from-layer="516" from-port="0" to-layer="517" to-port="1" />
		<edge from-layer="517" from-port="2" to-layer="519" to-port="2" />
		<edge from-layer="517" from-port="2" to-layer="528" to-port="2" />
		<edge from-layer="517" from-port="2" to-layer="531" to-port="0" />
		<edge from-layer="518" from-port="0" to-layer="519" to-port="3" />
		<edge from-layer="519" from-port="4" to-layer="521" to-port="2" />
		<edge from-layer="520" from-port="0" to-layer="521" to-port="3" />
		<edge from-layer="521" from-port="4" to-layer="522" to-port="1" />
		<edge from-layer="522" from-port="2" to-layer="536" to-port="0" />
		<edge from-layer="522" from-port="2" to-layer="540" to-port="1" />
		<edge from-layer="523" from-port="0" to-layer="524" to-port="0" />
		<edge from-layer="524" from-port="1" to-layer="535" to-port="0" />
		<edge from-layer="525" from-port="0" to-layer="528" to-port="0" />
		<edge from-layer="526" from-port="0" to-layer="528" to-port="1" />
		<edge from-layer="526" from-port="0" to-layer="532" to-port="1" />
		<edge from-layer="527" from-port="0" to-layer="528" to-port="3" />
		<edge from-layer="527" from-port="0" to-layer="532" to-port="3" />
		<edge from-layer="528" from-port="4" to-layer="534" to-port="1" />
		<edge from-layer="529" from-port="0" to-layer="532" to-port="0" />
		<edge from-layer="530" from-port="0" to-layer="531" to-port="1" />
		<edge from-layer="531" from-port="2" to-layer="532" to-port="2" />
		<edge from-layer="532" from-port="4" to-layer="534" to-port="2" />
		<edge from-layer="533" from-port="0" to-layer="534" to-port="3" />
		<edge from-layer="534" from-port="4" to-layer="535" to-port="1" />
		<edge from-layer="535" from-port="2" to-layer="536" to-port="1" />
		<edge from-layer="535" from-port="2" to-layer="540" to-port="0" />
		<edge from-layer="536" from-port="2" to-layer="539" to-port="0" />
		<edge from-layer="537" from-port="0" to-layer="538" to-port="0" />
		<edge from-layer="538" from-port="1" to-layer="539" to-port="1" />
		<edge from-layer="539" from-port="2" to-layer="541" to-port="0" />
		<edge from-layer="540" from-port="2" to-layer="541" to-port="1" />
		<edge from-layer="541" from-port="2" to-layer="544" to-port="0" />
		<edge from-layer="542" from-port="0" to-layer="543" to-port="0" />
		<edge from-layer="543" from-port="1" to-layer="544" to-port="1" />
		<edge from-layer="544" from-port="2" to-layer="546" to-port="0" />
		<edge from-layer="545" from-port="1" to-layer="546" to-port="1" />
		<edge from-layer="546" from-port="2" to-layer="547" to-port="0" />
	</edges>
	<rt_info>
		<MO_version value="2023.1.0-12185-9e6b00e51cd-releases/2023/1" />
		<Runtime_version value="2023.1.0-12185-9e6b00e51cd-releases/2023/1" />
		<conversion_parameters>
			<framework value="onnx" />
			<input_model value="DIR\yolov8.onnx" />
			<is_python_api_used value="True" />
			<model_name value="YOLOv8" />
		</conversion_parameters>
		<framework>
			<author value="Ultralytics" />
			<batch value="1" />
			<date value="2023-11-21T00:44:17.775342" />
			<description value="Ultralytics YOLOv8 model trained on C:\Users\<USER>\Desktop\yolov8\dataset\train_data\data.yaml" />
			<imgsz value="[384, 640]" />
			<license value="AGPL-3.0 https://ultralytics.com/license" />
			<names value="{0: 'Player', 1: 'Bush', 2: 'Enemy', 3: 'Cubebox'}" />
			<stride value="32" />
			<task value="detect" />
			<version value="8.0.184" />
		</framework>
		<legacy_frontend value="False" />
		<model_info>
			<iou_threshold value="0.7" />
			<labels value="Player Bush Enemy Cubebox" />
			<model_type value="YOLOv8" />
			<pad_value value="114" />
			<resize_type value="fit_to_window_letterbox" />
			<reverse_input_channels value="YES" />
			<scale_values value="255" />
		</model_info>
	</rt_info>
</net>
